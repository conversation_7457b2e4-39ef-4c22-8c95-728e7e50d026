DROP MATERIALIZED VIEW IF EXISTS "public"."SellerGroupSellersWithSearchPriorityExpanded";
CREATE MATERIALIZED VIEW "public"."SellerGroupSellersWithSearchPriorityExpanded" AS
WITH
  company_data AS (
    SELECT
      c.id,
      c.name,
      c.seller_group_id,
      c.primary_category,
      c.primary_sub_category,
      c.business_name,
      c.domain,
      c.website_status,
      c.lookup_sources,
      c.lookup_source,
      c.adr_country AS country,
      c.marketplace,
      c.amazon_seller_id,
      c.estimate_sales AS derived_estimate_sales,
      COUNT(
        CASE
          WHEN p.email_status IN ('CATCHALL', 'VERIFIED', 'GREYLISTING') THEN p.prospect_id
        END
      ) AS jeff_num_prospects,
      COUNT(
        CASE
          WHEN p.email_status = 'INCONCLUSIVE' THEN p.prospect_id
        END
      ) AS jeff_num_inconclusive,
      COUNT(
        CASE
          WHEN p.email_status = 'UNAVAILABLE' THEN p.prospect_id
        END
      ) AS jeff_num_unavailable,
      COUNT(
        CASE
          WHEN p.email_status = 'FAILED' THEN p.prospect_id
        END
      ) AS jeff_num_failed,
      COUNT(
        CASE
          WHEN p.email_status = 'UNVERIFIED' THEN p.prospect_id
        END
      ) AS jeff_num_unverified,
      CASE
      -- Handle China first (with NULL safety)
        WHEN c.adr_country = 'CN' THEN 'SP9'
        -- Handle NULL/zero/empty sales with Tier 1 countries
        WHEN (
          c.estimate_sales IS NULL
          OR c.estimate_sales = 0
        )
        AND COALESCE(c.adr_country, '') IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP4'
        -- Handle NULL/zero/empty sales with non-Tier 1 countries (exclude CN and NULL)
        WHEN (
          c.estimate_sales IS NULL
          OR c.estimate_sales = 0
        )
        AND COALESCE(c.adr_country, '') NOT IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP8'
        -- Sales $1-$10,000 with Tier 1 countries
        WHEN c.estimate_sales > 0
        AND c.estimate_sales <= 10000
        AND COALESCE(c.adr_country, '') IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP3B'
        -- Sales $10,001-$19,999 with Tier 1 countries
        WHEN c.estimate_sales > 10000
        AND c.estimate_sales < 20000
        AND COALESCE(c.adr_country, '') IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP3A'
        -- Sales $1-$19,999 with non-Tier 1 countries (exclude CN and NULL)
        WHEN c.estimate_sales > 0
        AND c.estimate_sales < 20000
        AND COALESCE(c.adr_country, '') NOT IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP7'
        -- Sales $20,000-$49,999 with Tier 1 countries
        WHEN c.estimate_sales > 20000
        AND c.estimate_sales < 50000
        AND COALESCE(c.adr_country, '') IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP2'
        -- Sales $20,000-$49,999 with non-Tier 1 countries (exclude CN and NULL)
        WHEN c.estimate_sales > 20000
        AND c.estimate_sales < 50000
        AND COALESCE(c.adr_country, '') NOT IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP6'
        -- Sales $50,000+ with Tier 1 countries
        WHEN c.estimate_sales > 50000
        AND COALESCE(c.adr_country, '') IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP1'
        -- Sales $50,000+ with non-Tier 1 countries (exclude CN and NULL)
        WHEN c.estimate_sales > 50000
        AND COALESCE(c.adr_country, '') NOT IN (
          'US',
          'GB',
          'AU',
          'IL',
          'DE',
          'ES',
          'FR',
          'NL',
          'NZ',
          'IE',
          'CH',
          'EE',
          'SE',
          'BE',
          'CA'
        ) THEN 'SP5'
        ELSE NULL
      END AS jeff_search_priority
    FROM
      "public"."AmazonSeller" c
      LEFT JOIN "public"."AmazonProspect" p ON p.seller_id = c.id
      where c.adr_country <> 'CN'
    GROUP BY
      c.id,
      c.name,
      c.amazon_seller_id,
      c.marketplace,
      c.seller_group_id,
      c.primary_category,
      c.primary_sub_category,
      c.business_name,
      c.adr_state,
      c.domain,
      c.website_status,
      c.lookup_sources,
      c.lookup_source,
      c.adr_country,
      c.estimate_sales
  )
SELECT
  *,
  CASE
    WHEN jeff_num_prospects = 0 THEN jeff_search_priority || ':0'
    WHEN jeff_search_priority IN ('SP5', 'SP6', 'SP7', 'SP8', 'SP9') THEN jeff_search_priority || ':4'
    WHEN jeff_num_prospects BETWEEN 1 AND 3  THEN jeff_search_priority || ':4'
    WHEN jeff_num_prospects >= 4 THEN jeff_search_priority || ':4+'
    ELSE jeff_search_priority
  END AS jeff_send_priority,
  MIN(jeff_search_priority) OVER (PARTITION BY seller_group_id) AS dominant_search_priority
FROM
  company_data;


DROP INDEX IF EXISTS idx_sellers_search_priority;
DROP INDEX IF EXISTS idx_sellers_jeff_send_priority;
DROP INDEX IF EXISTS idx_sellers_marketplace;
DROP INDEX IF EXISTS idx_sellers_marketplace_priority;
DROP INDEX IF EXISTS idx_sellers_amazon_seller_id;
DROP INDEX IF EXISTS idx_sellers_seller_group_id;
DROP INDEX IF EXISTS idx_sellers_lookup_sources;

-- Indexes for the materialized view
-- Run these AFTER creating the materialized view
-- Primary index on search priority (most important for filtering)
CREATE INDEX IF NOT EXISTS idx_sellers_search_priority ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (jeff_search_priority);

-- Index on Jeff send priority
CREATE INDEX IF NOT EXISTS idx_sellers_jeff_send_priority ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (jeff_send_priority);


-- Index on marketplace for marketplace-specific queries
CREATE INDEX IF NOT EXISTS idx_sellers_marketplace ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (marketplace);


-- Composite index for marketplace + priority queries
CREATE INDEX IF NOT EXISTS idx_sellers_marketplace_priority ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (marketplace, jeff_search_priority);


-- Index on seller ID for lookups
CREATE INDEX IF NOT EXISTS idx_sellers_amazon_seller_id ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (amazon_seller_id);


-- Index on seller group ID 
CREATE INDEX IF NOT EXISTS idx_sellers_seller_group_id ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (seller_group_id);


-- GIN index for JSON lookup_sources field
CREATE INDEX IF NOT EXISTS idx_sellers_lookup_sources ON "public"."SellerGroupSellersWithSearchPriorityExpanded" USING GIN (lookup_sources);

-- Index on seller group search priority
CREATE INDEX IF NOT EXISTS idx_sellers_seller_group_search_priority ON "public"."SellerGroupSellersWithSearchPriorityExpanded" (seller_group_jeff_search_priority);