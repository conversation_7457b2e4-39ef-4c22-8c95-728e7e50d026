/**
 * Simple test to verify minimal tagging works correctly
 */

require('dotenv').config();
const validateHtmlText = require('./services/generateLeads/validaterMethods/validateHtmlText');
const { validateImage } = require('./services/generateLeads/validaterMethods/validateImage');
const { completionFactory } = require('./services/scrapeGPT/factory');

async function testMinimalTagging() {
  console.log('🧪 Testing Minimal Tagging Implementation');
  console.log('=' .repeat(50));
  
  try {
    // Test 1: HTML Validation with minimal tags
    console.log('\n1️⃣ Testing HTML Validation...');
    const htmlResult = await validateHtmlText(
      'https://example.com',
      ['Example', 'Test'],
      { batchId: 'test_batch_001' }
    );
    
    console.log('✅ HTML Validation successful');
    console.log('🏷️  Expected tags: validator:htmlText, operationType:html_validation, batchId:test_batch_001');
    console.log('🏷️  Actual tags:', htmlResult.metadata?.tags?.filter(tag => 
      tag.includes('validator:') || tag.includes('operationType:') || tag.includes('batchId:') || tag.includes('type:')
    ));
    
    // Test 2: Completion Factory with minimal tags
    console.log('\n2️⃣ Testing Completion Factory...');
    const factoryResult = await completionFactory(
      'match_text',
      { textContent: 'Test content', businessKeywords: ['Test'] },
      null,
      true,
      { 
        operationType: 'test_operation',
        batchId: 'factory_batch_002',
        customTags: ['type:match_text']
      }
    );
    
    console.log('✅ Completion Factory successful');
    console.log('🏷️  Expected tags: type:match_text, operationType:test_operation, batchId:factory_batch_002');
    console.log('🏷️  Actual tags:', factoryResult.metadata?.tags?.filter(tag => 
      tag.includes('type:') || tag.includes('operationType:') || tag.includes('batchId:')
    ));
    
    console.log('\n🎉 Minimal tagging test completed successfully!');
    console.log('✅ Only essential tags are being tracked:');
    console.log('   - validator:htmlText / validator:image');
    console.log('   - operationType');
    console.log('   - batchId');
    console.log('   - type');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testMinimalTagging();
