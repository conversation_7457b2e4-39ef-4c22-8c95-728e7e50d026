/**
 * Test script to verify LiteLLM integration with standardized tagging
 * 
 * This script tests the updated scrapeGPT services to ensure they properly
 * use LiteLLM with the standardized tagging format: [sellerbot, service_name, function_name]
 */

require('dotenv').config();
const { getChatGPTResponse } = require('./services/scrapeGPT/request');
const { getAssistantResponse } = require('./services/scrapeGPT/assistant');
const { completionFactory } = require('./services/scrapeGPT/factory');

async function testChatGPTResponse() {
  console.log('\n🧪 Testing getChatGPTResponse with LiteLLM...');
  
  try {
    const result = await getChatGPTResponse(
      "You are a helpful assistant that analyzes business content.",
      "Analyze this text: 'Welcome to TechCorp, a leading software company.'",
      {
        useCase: 'test_analysis',
        domain: 'techcorp.com',
        customTags: ['test:chatgpt', 'priority:high']
      }
    );
    
    console.log('✅ ChatGPT Response successful');
    console.log('📝 Message:', result.message.substring(0, 100) + '...');
    console.log('📊 Usage:', result.usage);
    console.log('🏷️  Tags:', result.metadata.tags.slice(0, 5), '...');
    
    return true;
  } catch (error) {
    console.error('❌ ChatGPT Response failed:', error.message);
    return false;
  }
}

async function testAssistantResponse() {
  console.log('\n🧪 Testing getAssistantResponse with LiteLLM...');
  
  // Note: This will fail if the assistant doesn't exist, but we can test the tagging
  try {
    const result = await getAssistantResponse(
      'asst_test123', // This assistant probably doesn't exist
      JSON.stringify({ test: 'data' }),
      {
        useCase: 'test_assistant',
        customTags: ['test:assistant', 'priority:medium']
      }
    );
    
    console.log('✅ Assistant Response successful');
    console.log('📝 Message:', result.message);
    console.log('🏷️  Tags:', result.metadata.tags.slice(0, 5), '...');
    
    return true;
  } catch (error) {
    console.log('⚠️  Assistant Response expected to fail (assistant doesn\'t exist)');
    console.log('🔍 Error contains expected info:', error.message.includes('assistant') || error.message.includes('not found'));
    return true; // Expected failure
  }
}

async function testCompletionFactory() {
  console.log('\n🧪 Testing completionFactory with LiteLLM...');
  
  try {
    const result = await completionFactory(
      'match_text',
      {
        textContent: 'Welcome to Amazon Seller Solutions, your trusted partner.',
        businessKeywords: ['Amazon', 'Seller Solutions'],
        url: 'https://example.com'
      },
      null, // No assistant
      true, // Stringify
      {
        useCase: 'test_factory',
        batchId: 'test_batch_001',
        customTags: ['test:factory', 'priority:low']
      }
    );
    
    console.log('✅ Completion Factory successful');
    console.log('📝 Message:', result.message.substring(0, 100) + '...');
    console.log('📊 Usage:', result.usage);
    console.log('🏷️  Tags:', result.metadata.tags.slice(0, 5), '...');
    
    return true;
  } catch (error) {
    console.error('❌ Completion Factory failed:', error.message);
    return false;
  }
}

async function testTaggingFormat() {
  console.log('\n🧪 Testing standardized tagging format...');
  
  try {
    const result = await getChatGPTResponse(
      "You are a test assistant.",
      "Say hello",
      {
        useCase: 'tagging_test',
        customTags: ['format:test']
      }
    );
    
    const tags = result.metadata.tags;
    
    // Check if first three tags follow the format: [sellerbot, service_name, function_name]
    const hasCorrectFormat = (
      tags[0] === 'sellerbot' &&
      tags[1] === 'scrapeGPT' &&
      tags[2] === 'getChatGPTResponse'
    );
    
    if (hasCorrectFormat) {
      console.log('✅ Tagging format is correct: [sellerbot, scrapeGPT, getChatGPTResponse]');
      console.log('🏷️  Full tags:', tags);
      return true;
    } else {
      console.error('❌ Tagging format is incorrect');
      console.error('Expected: [sellerbot, scrapeGPT, getChatGPTResponse]');
      console.error('Got:', tags.slice(0, 3));
      return false;
    }
  } catch (error) {
    console.error('❌ Tagging format test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting LiteLLM Integration Tests');
  console.log('=' .repeat(50));
  
  const results = [];
  
  // Test individual functions
  results.push(await testChatGPTResponse());
  results.push(await testAssistantResponse());
  results.push(await testCompletionFactory());
  results.push(await testTaggingFormat());
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(50));
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! LiteLLM integration is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the configuration and try again.');
  }
  
  console.log('\n💡 Next steps:');
  console.log('1. Verify LITELLM_API_KEY is set in your environment');
  console.log('2. Check that https://ai.gateway.equalcollective.com is accessible');
  console.log('3. Review the tagging format in your LiteLLM dashboard');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testChatGPTResponse,
  testAssistantResponse,
  testCompletionFactory,
  testTaggingFormat,
  runAllTests
};
