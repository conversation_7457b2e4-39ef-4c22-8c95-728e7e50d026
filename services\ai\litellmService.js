/**
 * LiteLLM Service with Comprehensive Metadata Tagging
 * 
 * This service provides a wrapper around LiteLLM proxy with rich metadata
 * for tracking and observability in Lunary and other monitoring tools.
 */

require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');
const { aiMetadataHelper, metadataGenerators } = require('../../utils/aiMetadataHelper');

class LiteLLMService {
    constructor(options = {}) {
        // Use the new LiteLLM gateway URL
        this.proxyUrl = options.proxyUrl || process.env.LITELLM_PROXY_URL || 'https://ai.gateway.equalcollective.com';
        this.apiKey = options.apiKey || process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY || 'sk-litellm-default';

        // Initialize OpenAI client with LiteLLM gateway
        this.client = new OpenAI({
            apiKey: this.apiKey,
            baseURL: `${this.proxyUrl}/v1`
        });

        // Available models for the SellerBot API key
        this.availableModels = ['gpt-4o', 'gemini/gemini-2.5-flash', 'gemini/gemini-2.5-pro'];
        this.defaultModel = options.defaultModel || 'gpt-4o';

        // Initialize available models
        this.initializeModels();
    }

    /**
     * Initialize available models from the proxy
     */
    async initializeModels() {
        try {
            const response = await axios.get(`${this.proxyUrl}/v1/models`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                timeout: 10000
            });
            
            this.availableModels = response.data.data || [];
            console.log(`LiteLLM Service: Loaded ${this.availableModels.length} available models`);
        } catch (error) {
            console.warn('LiteLLM Service: Could not load available models:', error.message);
            this.availableModels = [];
        }
    }

    /**
     * Get the best available model for a given preference
     */
    getBestModel(preferredModel = null) {
        if (preferredModel && this.availableModels.some(m => m.id === preferredModel)) {
            return preferredModel;
        }
        
        if (this.availableModels.length === 0) {
            return this.defaultModel;
        }
        
        const modelIds = this.availableModels.map(m => m.id);
        
        // Preference order
        const preferences = ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-sonnet-20240229'];
        
        for (const pref of preferences) {
            const found = modelIds.find(id => id.includes(pref));
            if (found) return found;
        }
        
        return modelIds[0] || this.defaultModel;
    }

    /**
     * Create chat completion with comprehensive metadata in LiteLLM format
     */
    async createChatCompletion(messages, options = {}) {
        const startTime = Date.now();

        // Select best model
        const modelToUse = this.getBestModel(options.model);

        // Generate LiteLLM compatible request body with metadata
        const requestBody = aiMetadataHelper.generateLiteLLMRequestBody(messages, {
            ...options,
            model: modelToUse,
            operationType: 'chat_completion'
        });

        try {
            // Log metadata if enabled
            aiMetadataHelper.logMetadata({
                operation: 'createChatCompletion',
                model: modelToUse,
                user: requestBody.user,
                tags: requestBody.metadata.tags,
                messages_count: messages.length
            }, 'info');

            // Make the request with LiteLLM format
            const completion = await this.client.chat.completions.create(requestBody);

            const duration = Date.now() - startTime;

            // Prepare result with metadata
            const result = {
                success: true,
                message: completion.choices[0]?.message?.content,
                usage: completion.usage,
                duration,
                model: modelToUse,
                provider: 'litellm-proxy',
                metadata: {
                    ...requestBody.metadata,
                    response: {
                        id: completion.id,
                        created: completion.created,
                        finish_reason: completion.choices[0]?.finish_reason,
                        duration_ms: duration
                    }
                }
            };

            // Log successful completion
            aiMetadataHelper.logMetadata({
                operation: 'createChatCompletion',
                status: 'success',
                duration_ms: duration,
                tokens_used: completion.usage?.total_tokens,
                request_id: requestBody.metadata.request_id
            }, 'info');

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;

            // Log error with metadata
            aiMetadataHelper.logMetadata({
                operation: 'createChatCompletion',
                status: 'error',
                error_message: error.message,
                error_type: error.constructor.name,
                duration_ms: duration,
                request_id: requestBody.metadata.request_id
            }, 'error');

            return {
                success: false,
                error: error.message,
                provider: 'litellm-proxy',
                duration,
                metadata: {
                    ...requestBody.metadata,
                    error: {
                        message: error.message,
                        type: error.constructor.name,
                        duration_ms: duration
                    }
                }
            };
        }
    }

    /**
     * Create streaming chat completion with metadata in LiteLLM format
     */
    async createStreamingCompletion(messages, options = {}) {
        const modelToUse = this.getBestModel(options.model);

        // Generate LiteLLM compatible request body with metadata
        const requestBody = aiMetadataHelper.generateLiteLLMRequestBody(messages, {
            ...options,
            model: modelToUse,
            operationType: 'streaming_completion',
            stream: true
        });

        // Log metadata if enabled
        aiMetadataHelper.logMetadata({
            operation: 'createStreamingCompletion',
            model: modelToUse,
            user: requestBody.user,
            tags: requestBody.metadata.tags,
            streaming: true
        }, 'info');

        return await this.client.chat.completions.create(requestBody);
    }

    /**
     * ScrapeGPT compatible method with SellerBot tagging in LiteLLM format
     */
    async getChatGPTResponse(systemPrompt, userPrompt, options = {}) {
        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.createChatCompletion(messages, {
            useCase: options.useCase || 'scrape_analysis',
            feature: options.feature || 'content_analysis',
            operationType: 'scrape_gpt',
            temperature: options.temperature || 1,
            presence_penalty: options.presence_penalty || 0,
            top_p: options.top_p || 1,
            max_tokens: options.max_tokens || 256,
            // SellerBot-specific tagging
            customTags: [
                'service:SellerBot',
                'function:getChatGPTResponse',
                `useCase:${options.useCase || 'scrape_analysis'}`,
                `promptType:scrape_analysis`,
                `systemPromptLength:${systemPrompt.length}`,
                `userPromptLength:${userPrompt.length}`,
                ...(Array.isArray(options.customTags) ? options.customTags : [])
            ],
            ...options
        });
    }

    /**
     * Factory method compatible with existing patterns
     */
    async completionFactory(type, data, options = {}) {
        const metadata = metadataGenerators.centralizedAI({
            useCase: 'factory_completion',
            feature: 'template_completion',
            operationType: 'factory_completion',
            customTags: {
                factory_type: type,
                data_size: JSON.stringify(data).length,
                template_type: type
            },
            ...options
        });

        // This would integrate with your existing template system
        // For now, we'll create a simple completion
        const messages = [
            {
                role: 'system',
                content: `You are processing a ${type} request. Analyze the provided data and respond appropriately.`
            },
            {
                role: 'user',
                content: typeof data === 'string' ? data : JSON.stringify(data)
            }
        ];

        return await this.createChatCompletion(messages, {
            ...options,
            useCase: 'factory_completion',
            feature: type,
            customTags: metadata.tags
        });
    }

    /**
     * Health check with metadata
     */
    async getHealthStatus() {
        const metadata = metadataGenerators.centralizedAI({
            operationType: 'health_check',
            feature: 'system_health'
        });

        try {
            const response = await axios.get(`${this.proxyUrl}/health`, {
                headers: aiMetadataHelper.generateHeaders({
                    operationType: 'health_check'
                }),
                timeout: 10000
            });

            aiMetadataHelper.logMetadata({
                ...metadata,
                status: 'healthy',
                proxy_status: response.status
            }, 'info');

            return {
                status: 'healthy',
                proxy: response.data,
                models_available: this.availableModels.length,
                metadata
            };
        } catch (error) {
            aiMetadataHelper.logMetadata({
                ...metadata,
                status: 'unhealthy',
                error: error.message
            }, 'error');

            return {
                status: 'unhealthy',
                error: error.message,
                metadata
            };
        }
    }

    /**
     * Get available models with metadata
     */
    async getAvailableModels() {
        await this.initializeModels();
        
        const metadata = metadataGenerators.centralizedAI({
            operationType: 'models_list',
            feature: 'model_discovery'
        });

        aiMetadataHelper.logMetadata({
            ...metadata,
            models_count: this.availableModels.length
        }, 'info');

        return this.availableModels;
    }
}

// Create singleton instance
const litellmService = new LiteLLMService();

module.exports = {
    LiteLLMService,
    litellmService
};
